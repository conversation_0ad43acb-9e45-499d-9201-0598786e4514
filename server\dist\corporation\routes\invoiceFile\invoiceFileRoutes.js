"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/invoiceFile/create");
const view_1 = require("../../controllers/invoiceFile/view");
const viewAll_1 = require("../../controllers/invoiceFile/viewAll");
const viewByUser_1 = require("../../controllers/invoiceFile/viewByUser");
const update_1 = require("../../controllers/invoiceFile/update");
const delete_1 = require("../../controllers/invoiceFile/delete");
const authentication_1 = require("../../../middleware/authentication");
const bulkAssign_1 = require("../../controllers/invoiceFile/bulkAssign");
const bulkDelete_1 = require("../../controllers/invoiceFile/bulkDelete");
const checkUniqueFiles_1 = require("../../controllers/invoiceFile/checkUniqueFiles");
const viewTracksheetsById_1 = require("../../controllers/invoiceFile/viewTracksheetsById");
const router = (0, express_1.Router)();
router.get("/:id/tracksheets", authentication_1.authenticate, viewTracksheetsById_1.viewTracksheetsById);
router.post("/bulk", authentication_1.authenticate, create_1.createBulkInvoiceFiles);
router.get("/check-unique", authentication_1.authenticate, checkUniqueFiles_1.checkBulkUniqueInvoiceFiles);
router.post("/bulk-assign", authentication_1.authenticate, bulkAssign_1.bulkAssignInvoiceFiles);
router.post("/bulk-delete", authentication_1.authenticate, bulkDelete_1.bulkDeleteInvoiceFiles);
router.post("/", authentication_1.authenticate, create_1.createInvoiceFile);
router.get("/:id", authentication_1.authenticate, view_1.viewInvoiceFile);
router.get("/", authentication_1.authenticate, viewAll_1.viewAllInvoiceFiles);
router.get("/user/:userId", authentication_1.authenticate, viewByUser_1.viewInvoiceFilesByUser);
router.put("/:id", authentication_1.authenticate, update_1.updateInvoiceFile);
router.delete("/:id", authentication_1.authenticate, delete_1.deleteInvoiceFile);
exports.default = router;
//# sourceMappingURL=invoiceFileRoutes.js.map