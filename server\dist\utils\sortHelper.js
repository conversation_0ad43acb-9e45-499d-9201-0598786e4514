"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrderBy = getOrderBy;
function getOrderBy(sortByArr, orderArr, allowedFields) {
    if (!allowedFields.length) {
        throw new Error("No allowed fields defined for sorting.");
    }
    // Ensure arrays are properly handled
    if (!Array.isArray(sortByArr)) {
        sortByArr = sortByArr ? [String(sortByArr)] : [];
    }
    if (!Array.isArray(orderArr)) {
        orderArr = orderArr ? [String(orderArr)] : [];
    }
    // Filter out empty or invalid fields
    const validSortFields = sortByArr.filter(field => field && String(field).trim());
    if (validSortFields.length === 0) {
        // Return default sorting by first allowed field if no valid sort fields
        return [{ [allowedFields[0]]: "asc" }];
    }
    return validSortFields.map((rawField, idx) => {
        const inputField = String(rawField).trim().toLowerCase();
        const orderValue = orderArr[idx] ? String(orderArr[idx]).trim().toLowerCase() : "asc";
        const sortOrder = orderValue === "desc" ? "desc" : "asc";
        // Find matching allowed field in a case-insensitive way
        const matchedField = allowedFields.find((f) => f.toLowerCase() === inputField) || allowedFields[0];
        if (matchedField.includes(".")) {
            const [relation, nestedField] = matchedField.split(".");
            return {
                [relation]: {
                    [nestedField]: sortOrder,
                },
            };
        }
        return { [matchedField]: sortOrder };
    });
}
//# sourceMappingURL=sortHelper.js.map